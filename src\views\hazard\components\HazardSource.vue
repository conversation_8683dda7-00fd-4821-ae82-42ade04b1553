<template>
  <div class="panel-container">
    <div class="panel-header">重点防控点位</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item">
          <div class="status-value">36</div>
          <div class="status-label">一级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">22</div>
          <div class="status-label">二级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">10</div>
          <div class="status-label">三级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">12</div>
          <div class="status-label">四级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">6</div>
          <div class="status-label">四级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">40</div>
          <div class="status-label">四级重大危险源</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped>
@import '@/styles/index.css';

.status-indicators {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.status-item {
  width: 31%;
  height: 104px;
  background: url('@/assets/hazard/hazard-source-icon.png') no-repeat center bottom;
  background-size: 140px 70px;
}

.status-value {
  font-family: Noto Sans SC;
  font-size: 24px;
  font-weight: bold;
  line-height: 32px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  letter-spacing: normal;
  color: #fff;
}

.status-unit {
  font-family: Noto Sans SC;
  font-size: 16px;
  font-weight: normal;
  line-height: 24px;
  text-align: center;
  color: #ffffff;
}
</style>
