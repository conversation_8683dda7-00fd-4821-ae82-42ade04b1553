<script setup lang="ts">
import { watch, computed, ref } from 'vue'
import CustomCheckbox from '@/components/ui/checkbox/CustomCheckbox.vue'

// 定义UE命令的数据结构
interface UeData {
  command: string
  args: Record<string, any>
}

interface LayerItem {
  id: string
  label: string
  checked: boolean
  ueData?: UeData[]
  ueRemoveData?: UeData
}

interface LayerGroup {
  label: string
  icon: string
  checked: boolean | 'indeterminate'
  children?: LayerItem[]
  ueData?: UeData[]
  ueRemoveData?: UeData
}

interface Layers {
  [key: string]: LayerGroup
}

// 导入图标资源
import gasIcon from '@/assets/map/gas-icon.png'
import activeGasIcon from '@/assets/map/active-gas-icon.png'
import pipelineIcon from '@/assets/map/pipeline-icon.png'
import activePipelineIcon from '@/assets/map/active-pipeline-icon.png'
import stationIcon from '@/assets/map/station-icon.png'
import activeStationIcon from '@/assets/map/active-station-icon.png'
import warningIcon from '@/assets/map/warning-icon.png'
import activeWarningIcon from '@/assets/map/active-warning-icon.png'
import alarmIcon from '@/assets/map/alarm-icon.png'
import activeAlarmIcon from '@/assets/map/active-alarm-icon.png'
import resourceIcon from '@/assets/map/resource-icon.png'
import activeResourceIcon from '@/assets/map/active-resource-icon.png'
import eventIcon from '@/assets/map/event-icon.png'
import activeEventIcon from '@/assets/map/active-event-icon.png'
import falloutIcon from '@/assets/map/fallout-icon.png'
import activeFalloutIcon from '@/assets/map/active-fallout-icon.png'
import buildingIcon from '@/assets/map/building-icon.png'
import activeBuildingIcon from '@/assets/map/active-building-icon.png'
import industryIcon from '@/assets/map/industry-icon.png'
import activeIndustryIcon from '@/assets/map/active-industry-icon.png'

// 图标配置
const ICON_CONFIG = {
  gas: {
    default: gasIcon,
    active: activeGasIcon,
  },
  pipeline: {
    default: pipelineIcon,
    active: activePipelineIcon,
  },
  station: {
    default: stationIcon,
    active: activeStationIcon,
  },
  warning: {
    default: warningIcon,
    active: activeWarningIcon,
  },
  alarm: {
    default: alarmIcon,
    active: activeAlarmIcon,
  },
  resource: {
    default: resourceIcon,
    active: activeResourceIcon,
  },
  event: {
    default: eventIcon,
    active: activeEventIcon,
  },
  fallout: {
    default: falloutIcon,
    active: activeFalloutIcon,
  },
  building: {
    default: buildingIcon,
    active: activeBuildingIcon,
  },
  industry: {
    default: industryIcon,
    active: activeIndustryIcon,
  },
} as const

// 颜色配置
const COLORS = {
  checked: '#FFD966',
  unchecked: '#66FFFF',
} as const

const props = defineProps<{
  modelValue: Layers
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: Layers): void
}>()

// 用于跟踪上一次的状态，避免深拷贝
const previousState = ref<Map<string, boolean>>(new Map())

const localLayers = computed({
  get: () => props.modelValue,
  set: value => {
    emit('update:modelValue', value)
  },
})

// 优化的UE数据发送函数
// const sendUeData = (data: UeData | UeData[]) => {
// if (Array.isArray(data)) {
//   data.forEach(sendToUe)
// } else {
//   sendToUe(data)
// }
// }

// 处理单个项目的UE交互
const handleUeInteraction = (item: LayerItem | LayerGroup, isChecked: boolean) => {
  if (isChecked && item.ueData) {
    // sendUeData(item.ueData)
  } else if (!isChecked && item.ueRemoveData) {
    // sendToUe(item.ueRemoveData)
  }
}

// 生成状态键
const generateStateKey = (groupName: string, itemId?: string) => {
  return itemId ? `${groupName}-${itemId}` : groupName
}

// 初始化状态跟踪
const initializeState = () => {
  const initialState = new Map<string, boolean>()
  Object.entries(props.modelValue).forEach(([groupName, group]) => {
    const groupKey = generateStateKey(groupName)
    initialState.set(groupKey, group.checked === true)

    if (group.children) {
      group.children.forEach(child => {
        const childKey = generateStateKey(groupName, child.id)
        initialState.set(childKey, child.checked)
      })
    }
  })
  previousState.value = initialState
}

// 优化的监听逻辑 - 使用更高效的状态跟踪
watch(
  () => props.modelValue,
  (newLayers, oldLayers) => {
    // 首次初始化
    if (!oldLayers) {
      initializeState()
      return
    }

    const currentState = new Map<string, boolean>()

    // 收集当前状态并检查变化
    Object.entries(newLayers).forEach(([groupName, group]) => {
      const groupKey = generateStateKey(groupName)
      const groupChecked = group.checked === true

      // 检查组级别的变化
      const prevChecked = previousState.value.get(groupKey)
      if (prevChecked !== undefined && groupChecked !== prevChecked) {
        // 如果没有子项且有UE交互，处理组级别的UE交互
        if (!group.children) {
          handleUeInteraction(group, groupChecked)
        }
      }
      currentState.set(groupKey, groupChecked)

      // 处理子项
      if (group.children) {
        group.children.forEach(child => {
          const childKey = generateStateKey(groupName, child.id)
          const prevChildChecked = previousState.value.get(childKey)

          if (prevChildChecked !== undefined && child.checked !== prevChildChecked) {
            handleUeInteraction(child, child.checked)
          }
          currentState.set(childKey, child.checked)
        })
      }
    })

    // 更新状态
    previousState.value = currentState
  },
  { deep: true, immediate: true },
)

// 优化的组切换函数
const toggleGroup = (groupName: keyof Layers) => {
  const newLayers = { ...localLayers.value }
  const group = newLayers[groupName]

  if (!group.children) {
    localLayers.value = newLayers
    return
  }

  const newCheckedState = group.checked === 'indeterminate' ? true : group.checked

  // 批量更新子项状态
  group.children.forEach(child => {
    child.checked = newCheckedState
  })

  localLayers.value = newLayers
}

// 优化的子项变化处理函数
const handleChildChange = (groupName: keyof Layers) => {
  const newLayers = { ...localLayers.value }
  const group = newLayers[groupName]

  if (!group.children) {
    localLayers.value = newLayers
    return
  }

  const checkedCount = group.children.filter(child => child.checked).length
  const totalCount = group.children.length

  // 使用更简洁的逻辑确定组状态
  group.checked = checkedCount === 0 ? false : checkedCount === totalCount ? true : 'indeterminate'

  localLayers.value = newLayers
}

// 获取图标样式的计算函数
const getIconStyle = (iconType: string, isActive: boolean) => {
  const config = ICON_CONFIG[iconType as keyof typeof ICON_CONFIG]
  if (!config) {
    console.warn(`Icon config not found for type: ${iconType}`)
    return {
      width: '16px',
      height: '16px',
      display: 'inline-block',
      backgroundColor: '#ccc', // 占位颜色，便于调试
    }
  }

  const iconUrl = isActive ? config.active : config.default
  return {
    backgroundImage: `url("${iconUrl}")`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    backgroundSize: 'contain',
    width: '16px',
    height: '16px',
    display: 'inline-block',
  }
}

// 获取文本颜色的计算函数
const getTextColor = (checked: boolean | 'indeterminate') => {
  return checked === false ? COLORS.unchecked : COLORS.checked
}
</script>
<template>
  <div class="w-[154px] p-4 wrapper">
    <div class="flex flex-col gap-2">
      <div class="text-sm layer-group" v-for="(group, groupName) in localLayers" :key="String(groupName)">
        <div class="flex items-center space-x-2">
          <CustomCheckbox
            :id="String(groupName)"
            v-model="group.checked"
            @update:model-value="toggleGroup(String(groupName))"
          />
          <label
            :for="String(groupName)"
            class="flex items-center gap-2 text-sm font-medium leading-none cursor-pointer select-none"
            :style="{ color: getTextColor(group.checked) }"
          >
            <div class="checkbox-icon" :style="getIconStyle(group.icon, group.checked !== false)"></div>
            {{ group.label }}
          </label>
        </div>
        <div class="pl-6 mt-2 space-y-2" v-if="group.children">
          <div class="flex items-center space-x-2" v-for="item in group.children" :key="item.id">
            <CustomCheckbox
              :id="item.id"
              v-model="item.checked"
              @update:model-value="handleChildChange(String(groupName))"
            />
            <label
              :for="item.id"
              class="text-sm leading-none cursor-pointer select-none"
              :style="{ color: getTextColor(item.checked) }"
            >
              {{ item.label }}
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  background: url('@/assets/map/layers-tool-bg.svg') no-repeat 0 0;
  background-size: cover;
}

.checkbox-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.layer-group {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
  color: #66ffff;
}

.layer-group:last-child {
  border-bottom: none;
}
</style>
