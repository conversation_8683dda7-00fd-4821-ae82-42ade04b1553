import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'index',
      redirect: '/gas',
    },
    {
      path: '/gas',
      name: 'gas',
      meta: {
        title: '智慧燃气',
      },
      component: () => import('@/views/dashboard/index.vue'),
    },
    {
      path: '/run-monitor',
      name: 'runMon',
      meta: {
        title: '运行监测',
      },
      component: () => import('@/views/runMonitor/index.vue'),
    },
    {
      path: '/emergency',
      name: 'emergency',
      meta: {
        title: '应急管理',
      },
      component: () => import('@/views/emergency/index.vue'),
    },
    {
      path: '/evaluate',
      name: 'evaluate',
      meta: {
        title: '安全评价',
      },
      component: () => import('@/views/evaluate/index.vue'),
    },
    {
      path: '/hazard',
      name: 'hazard',
      meta: {
        title: '重点防控',
      },
      component: () => import('@/views/hazard/index.vue'),
    },
  ],
})

export default router
