<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <Tabs default-value="tab1">
        <TabsList
          class="grid w-16 grid-cols-1 absolute top-[50%] right-0 translate-x-[100%] translate-y-[-50%] border-[#409fff] bg-[#409fff]/15"
        >
          <TabsTrigger value="tab1" class="border-[#409fff] bg-[#409fff]/15 text-[#409fff]">tab1</TabsTrigger>
          <TabsTrigger value="tab2" class="border-[#409fff] bg-[#409fff]/15 text-[#409fff]">tab2</TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">
          <div class="flex flex-col gap-6">
            <SourceIndicators />
            <SourceEntry @click="onShowSourceEntryDialog" />
          </div>
        </TabsContent>
        <TabsContent value="tab2">
          <div class="flex flex-col gap-6">
            <OnlineRate />
            <OnlineVideo @click="onShowOnlineVideoDialog" />
            <PipelineLeakTable @click="onShowPipelineLeakDialog" />
          </div>
        </TabsContent>
      </Tabs>
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel-left">
      <LayersTool v-model="layersData" />
    </div>
    <div class="center-panel-right">
      <MapTool @click="onShowLeakModelDialog" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <Tabs default-value="tab1">
        <TabsList
          class="grid w-16 grid-cols-1 absolute top-[50%] right-[744px] translate-y-[-50%] border-[#409fff] bg-[#409fff]/15"
        >
          <TabsTrigger value="tab1" class="border-[#409fff] bg-[#409fff]/15 text-[#409fff]">tab1</TabsTrigger>
          <TabsTrigger value="tab2" class="border-[#409fff] bg-[#409fff]/15 text-[#409fff]">tab2</TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">
          <div class="flex flex-col gap-6">
            <PlanPie />
            <DrillsTop />
          </div>
        </TabsContent>
        <TabsContent value="tab2">
          <div class="flex flex-col gap-6">
            <PipelineLeakLine />
            <AccidentTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
    <!-- 资源录入 -->
    <SourceEntryDialog :open="showSourceEntryDialog" :data="sourceEntryInfo" @close="showSourceEntryDialog = false" />
    <!-- 泄漏预演 -->
    <LeakModelDialog :open="showLeakModelDialog" @close="showLeakModelDialog = false" />
    <!-- 管网泄漏 -->
    <PipelineLeakDialog
      :open="showPipelineLeakDialog"
      :data="pipelineLeakInfo"
      @close="showPipelineLeakDialog = false"
    />
    <!-- 在线视频 -->
    <VideoDialog :open="showVideoDialog" @close="showVideoDialog = false" :data="videoInfo" />

    <!-- UE弹窗 -->
    <EmergencyResourceDialog :open="resourceInfoOpen" @close="resourceInfoOpen = false" />
    <EmergencyEventsDialog :open="eventInfoOpen" @close="eventInfoOpen = false" />
    <StationDialog :open="stationInfoOpen" @close="stationInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ref, onMounted, onUnmounted, watch } from 'vue'
import SourceIndicators from './components/SourceIndicators.vue'
import SourceEntry from './components/SourceEntry.vue'
import OnlineRate from './components/OnlineRate.vue'
import OnlineVideo from './components/OnlineVideo.vue'
import PipelineLeakTable from './components/PipelineLeakTable.vue'
import PipelineLeakLine from './components/PipelineLeakLine.vue'
import PlanPie from './components/PlanPie.vue'
import DrillsTop from './components/DrillsTop.vue'
import AccidentTable from './components/AccidentTable.vue'
import MapTool from './components/MapTool.vue'
import LeakModelDialog from './LeakModelDialog.vue'
import SourceEntryDialog from './SourceEntryDialog.vue'
import PipelineLeakDialog from './PipelineLeakDialog.vue'
import VideoDialog from './VideoDialog.vue'
import LayersTool from '@/components/LayersTool.vue'
import EmergencyResourceDialog from '@/components/ue-dialog/EmergencyResourceDialog.vue'
import EmergencyEventsDialog from '@/components/ue-dialog/EmergencyEventsDialog.vue'
import StationDialog from '@/components/ue-dialog/StationDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import { layerData } from './layerData'

const showLeakModelDialog = ref<boolean>(false)
const showSourceEntryDialog = ref<boolean>(false)
const showPipelineLeakDialog = ref<boolean>(false)
const showVideoDialog = ref<boolean>(false)

const sourceEntryInfo = ref<any>({})
const pipelineLeakInfo = ref<any>({})
const videoInfo = ref<any>({
  id: '1',
  title: '--',
  address: '--',
  url: '',
})

const resourceInfoOpen = ref(false)
// const industryInfoData: Ref<IndustryInfo | null> = ref(null)
const stationInfoOpen = ref(false)
// const stationInfoData: Ref<StationInfo | null> = ref(null)
const pipelineInfoOpen = ref(false)
// const pipelineInfoData: Ref<PipelineInfo | null> = ref(null)
const eventInfoOpen = ref(false)
// const areaInfoData: Ref<AreaInfo | null> = ref(null)

// 图层数据
const layersData = ref(layerData)

// 监听layersData的变化
watch(layersData, newLayers => {
  console.log('Layers data updated:', newLayers)
  // 可以在这里处理图层数据的变化
})

const onShowLeakModelDialog = (value: boolean) => {
  console.log('Dialog visibility:', value)
  // 可以在这里处理弹窗显示逻辑
  showLeakModelDialog.value = true
}
const onShowSourceEntryDialog = (record: any) => {
  sourceEntryInfo.value = record
  showSourceEntryDialog.value = true
  console.log('Source entry record:', record)
}
const onShowPipelineLeakDialog = (record: any) => {
  console.log('Pipeline leak record:', record)
  pipelineLeakInfo.value = record
  showPipelineLeakDialog.value = true
}
const onShowOnlineVideoDialog = (video: any) => {
  console.log('Online video clicked:', video)
  videoInfo.value = video
  // 可以在这里处理弹窗显示逻辑
  showVideoDialog.value = true
}

// 交互处理
const handleClickPoint = async (data: { id: string; type?: string }) => {
  const { id } = data
  const idNumber = parseInt(id.replace(/\D/g, ''), 10)

  if (idNumber >= 12210 && idNumber <= 12223) {
    resourceInfoOpen.value = true
  } else if (idNumber >= 12271 && idNumber <= 12284) {
    stationInfoOpen.value = true
  } else {
    eventInfoOpen.value = true
  }
}

const handleClickLine = async () => {
  // pipelineInfoData.value = await fetchPipelineInfo(data.id)
  pipelineInfoOpen.value = true
}

onMounted(() => {
  // todo
})

onUnmounted(() => {
  // 清除所有图层
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
}
.left-panel {
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.right-panel {
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.center-panel-left {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

.center-panel-right {
  position: absolute;
  top: 0;
  right: 768px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
