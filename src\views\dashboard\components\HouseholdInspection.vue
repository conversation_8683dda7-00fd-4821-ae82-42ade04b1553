<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户巡查</div>
      <div class="header-dropdown">
        <Select v-model:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="统计周期" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <!-- <SelectLabel>Fruits</SelectLabel> -->
              <SelectItem value="2025">2025年</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="gauge-container">
        <div class="relative top-[-20px]">
          <div class="text-center">
            <span class="gauge-value">6</span>
            <span class="text-base">个</span>
          </div>
          <div class="gauge-label">巡检计划</div>
          <div class="gauge-circle-bg"></div>
        </div>
        <div class="relative top-[20px]">
          <div class="text-center">
            <span class="gauge-value">5</span>
            <span class="text-base">个</span>
          </div>
          <div class="gauge-label">巡检计划完成情况</div>
          <div class="gauge-circle-bg"></div>
        </div>
        <div class="relative top-[-20px]">
          <div class="text-center">
            <span class="gauge-value">83.33</span>
            <span class="text-base">%</span>
          </div>
          <div class="gauge-label">巡检完成率</div>
          <div class="gauge-circle-bg"></div>
        </div>
        <div class="relative top-[20px]">
          <div class="text-center">
            <span class="gauge-value">10</span>
            <span class="text-base">个</span>
          </div>
          <div class="gauge-label">入户安检计划</div>
          <div class="gauge-circle-bg"></div>
        </div>
        <div class="relative top-[-20px]">
          <div class="text-center">
            <span class="gauge-value">14.51</span>
            <span class="text-base">万户</span>
          </div>
          <div class="gauge-label">安检用户</div>
          <div class="gauge-circle-bg"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const selectedOpt = ref<string>('2025')
</script>

<style scoped>
@import '@/styles/index.css';

.header-title {
  color: white;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 8px;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.gauge-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  background: url('@/assets/household-inspection/indicator-con-bg.png') no-repeat center;
  background-size: cover;
}

.gauge-circle-bg {
  width: 72px;
  height: 60px;
  background: url('@/assets/household-inspection/indicator-bg.png') no-repeat center;
  background-size: cover;
  will-change: transform;
  /* animation: spin 5s linear infinite; */
}

.gauge-value {
  color: #fff;
  font-family: DINPro;
  font-size: 24px;
  font-weight: bold;
  z-index: 1;
}

.gauge-label {
  color: #66ffff;
  font-size: 14px;
  text-align: center;
}
</style>
