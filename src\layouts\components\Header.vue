<template>
  <div class="header-wrapper">
    <div class="flex items-center justify-center header-content">
      <div class="nav-tabs">
        <div class="nav-item" @click="navigateTo('/gas')" :class="{ active: currentPath === '/gas' }">
          <div class="nav-item-text">智慧燃气</div>
        </div>
        <div class="nav-item">
          <div class="nav-item-text">态势监管</div>
        </div>
        <div class="nav-item" @click="navigateTo('/run-monitor')" :class="{ active: currentPath === '/run-monitor' }">
          <div class="nav-item-text">运行监测</div>
        </div>
        <div class="nav-item">
          <div class="nav-item-text">巡查巡检</div>
        </div>
        <div class="nav-item">
          <div class="nav-item-text">瓶装石油气</div>
        </div>
        <div class="nav-item">
          <div class="nav-item-text">入户安检</div>
        </div>
        <!-- 标题名称占位 -->
        <div class="title-placeholder"></div>
        <div
          class="transform nav-item -scale-x-100"
          @click="navigateTo('/emergency')"
          :class="{ active: currentPath === '/emergency' }"
        >
          <div class="transform nav-item-text -scale-x-100">应急管理</div>
        </div>
        <div class="transform nav-item -scale-x-100">
          <div class="transform nav-item-text -scale-x-100">运输车辆</div>
        </div>
        <div class="transform nav-item -scale-x-100">
          <div class="transform nav-item-text -scale-x-100">双重预防</div>
        </div>
        <div class="transform nav-item -scale-x-100">
          <div class="transform nav-item-text -scale-x-100">投诉监管</div>
        </div>
        <div
          class="transform nav-item -scale-x-100"
          @click="navigateTo('/evaluate')"
          :class="{ active: currentPath === '/evaluate' }"
        >
          <div class="transform nav-item-text -scale-x-100">安全评价</div>
        </div>
        <div
          class="transform nav-item -scale-x-100"
          @click="navigateTo('/hazard')"
          :class="{ active: currentPath === '/hazard' }"
        >
          <div class="transform nav-item-text -scale-x-100">重点防控</div>
        </div>
      </div>
      <div class="settings-icon"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const currentPath = ref(route.path)

watch(
  () => route.path,
  newPath => {
    currentPath.value = newPath
  },
)

const navigateTo = (path: string) => {
  router.push({ path })
}
</script>

<style scoped>
.header-wrapper {
  width: 100%;
  height: 72px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background: url('@/assets/header/header-bg.png') no-repeat center;
  background-size: 100% 100%;
  backdrop-filter: blur(4px);
}

.header-content {
  position: relative;
  height: 100%;
  padding: 0 40px;
}

.title-placeholder {
  width: 700px;
  height: 100%;
}

.settings-icon {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: url('@/assets/header/settings.svg') no-repeat center;
  background-size: cover;
}

.nav-tabs {
  display: flex;
  align-items: center;
}

.nav-item {
  position: relative;
  width: 140px;
  height: 36px;
  font-family: Noto Sans SC;
  font-size: 16px;
  line-height: 36px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  background: url('@/assets/header/page-btn.png') no-repeat center;
  background-size: 100% 100%;
}

.nav-item:hover {
  filter: brightness(1.2);
  color: white;
}

.nav-item.active {
  background: url('@/assets/header/page-btn-active.png') no-repeat center;
  background-size: 100% 100%;
}
</style>
