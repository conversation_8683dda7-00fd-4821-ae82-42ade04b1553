.panel-container {
  width: 744px;
  height: 304px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-container-half {
  width: 360px;
  height: 304px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-half.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-container-col {
  width: 744;
  height: 468px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-bg-744x468.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-header {
  padding: 0 16px 0 52px;
  height: 48px;
  line-height: 44px; /* 注意：这里是44而不是48，不是错误 */
  text-align: left;
  font-family: MStiffHei PRC;
  font-size: 18px;
}
.panel-content {
  width: 100%;
  height: calc(100% - 48px);
  box-sizing: border-box;
}
