<template>
  <div class="panel-container">
    <div class="panel-header">重点防控点位上报数据</div>
    <div class="p-4 panel-content">
      <div class="flex gap-6">
        <!-- 左侧3D环形图 -->
        <div class="chart-section">
          <div ref="chartRef" class="donut-chart"></div>
          <!-- 图例 -->
          <!-- <div class="legend-section">
            <div class="legend-item">
              <div class="legend-color" style="background-color: #55d0e0"></div>
              <span class="legend-text">2020-2022</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background-color: #f7b731"></div>
              <span class="legend-text">2022-2023</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background-color: #5a9fd4"></div>
              <span class="legend-text">2024-2025</span>
            </div>
          </div> -->
        </div>

        <!-- 右侧数据列表 -->
        <div class="data-list-section">
          <div v-for="(item, index) in dataList" :key="index">
            <div class="flex items-center justify-between">
              <div class="data-row-label">{{ item.label }}</div>
              <div class="data-row-value">{{ item.value }}</div>
            </div>
            <div class="progress-section">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
// import { getPie3D } from '@/utils/echartsTools'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

// 右侧数据列表
const dataList = ref([
  {
    label: '这是对应内容',
    value: '33340',
    progress: 75,
  },
  {
    label: '这是对应内容',
    value: '33340',
    progress: 60,
  },
  {
    label: '这是对应内容',
    value: '33340',
    progress: 85,
  },
  {
    label: '这是对应内容',
    value: '33340',
    progress: 45,
  },
])

/*************************

*************************
【 getParametricEquation 函数说明 】 :
*************************
    根据传入的
    startRatio（浮点数）: 当前扇形起始比例，取值区间 [0, endRatio)
    endRatio（浮点数）: 当前扇形结束比例，取值区间 (startRatio, 1]
    isSelected（布尔值）:是否选中，效果参照二维饼图选中效果（单选）
    isHovered（布尔值）: 是否放大，效果接近二维饼图高亮（放大）效果（未能实现阴影）

    生成 3D 扇形曲面

*************************
【 getPie3D 函数说明 】 :
*************************
    根据传入的饼图数据，生成模拟 3D 饼图的配置项 option

    饼图数据格式示意：
    [{
        name: '数据1',
        value: 10
    }, {
        // 数据项名称
        name: '数据2',
        value : 56,
        itemStyle:{
            // 透明度
            opacity: 0.5,
            // 扇形颜色
            color: 'green'
        }
    }]

*************************
【 鼠标事件监听说明 】 :
*************************
    click： 实现饼图的选中效果（单选）
            大致思路是，通过监听点击事件，获取到被点击数据的系列序号 params.seriesIndex，
            然后将对应扇形向外/向内移动 10% 的距离。

    mouseover： 近似实现饼图的高亮（放大）效果
            大致思路是，在饼图外部套一层透明的圆环，然后监听 mouseover 事件，获取
            到对应数据的系列序号 params.seriesIndex 或系列名称 params.seriesName，
            如果鼠标移到了扇形上，则先取消高亮之前的扇形（如果有）,再高亮当前扇形；
            如果鼠标移到了透明圆环上，则只取消高亮之前的扇形（如果有），不做任何高亮。

    globalout： 当鼠标移动过快，直接划出图表区域时，有可能监听不到透明圆环的 mouseover，
            导致此前高亮没能取消，所以补充了对 globalout 的监听。


*************************/

// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2

  let startRadian = startRatio * Math.PI * 2
  let endRadian = endRatio * Math.PI * 2
  let midRadian = midRatio * Math.PI * 2

  // 如果只有一个扇形，则不实现选中效果。
  // if (startRatio === 0 && endRatio === 1) {
  //     isSelected = false;
  // }
  isSelected = false
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.sin(midRadian) * 0.1 : 0
  let offsetY = isSelected ? Math.cos(midRadian) * 0.1 : 0

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x: function (u, v) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    y: function (u, v) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
    },
  }
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData, internalDiameterRatio) {
  let series = []
  let sumValue = 0
  let startValue = 0
  let endValue = 0
  let legendData = []
  let k =
    typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value

    let seriesItem = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: 1 / 10,
      },
      avoidLabelOverlap: false,
      padAngle: 5,
    }

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle = {}

      typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
      typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null

      seriesItem.itemStyle = itemStyle
    }
    series.push(seriesItem)
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value

    series[i].pieData.startRatio = startValue / sumValue
    series[i].pieData.endRatio = endValue / sumValue
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value,
    )

    startValue = endValue

    legendData.push(series[i].name)
  }

  // 底座第一层 - 主底座
  series.push({
    name: 'baseSeries1',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.8,
      color: '#1a3a5c', // 深蓝色底座
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u, v) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.4
      },
      y: function (u, v) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.4
      },
      z: function (u, v) {
        return Math.cos(v) > 0 ? -0.8 : -1.2
      },
    },
  })

  // 底座第二层 - 中间层
  series.push({
    name: 'baseSeries2',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.9,
      color: '#2d4a6b', // 稍浅的蓝色
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u, v) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.2
      },
      y: function (u, v) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.2
      },
      z: function (u, v) {
        return Math.cos(v) > 0 ? -0.6 : -0.8
      },
    },
  })

  // 底座第三层 - 顶层
  series.push({
    name: 'baseSeries3',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.95,
      color: '#3e5a7a', // 更浅的蓝色
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u, v) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.0
      },
      y: function (u, v) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.0
      },
      z: function (u, v) {
        return Math.cos(v) > 0 ? -0.4 : -0.6
      },
    },
  })

  // 透明的交互层，用于鼠标事件
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0,
      color: 'transparent',
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u, v) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2
      },
      y: function (u, v) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2
      },
      z: function (u, v) {
        return Math.cos(v) > 0 ? -0.2 : -0.4
      },
    },
  })

  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  const option = {
    legend: {
      tooltip: {
        show: true,
      },
      data: pieData.map((i: any) => i.name),
      bottom: '5%',
      itemGap: 8,
      itemWidth: 12,
      itemHeight: 8,
      icon: 'rect',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    animation: true,
    tooltip: {
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          return `${params.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>${option.series[params.seriesIndex].pieData.value}`
        }
      },
      textStyle: {
        fontSize: 12,
      },
    },
    title: {
      x: 'center',
      top: '20',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    backgroundColor: 'transparent',
    labelLine: {
      show: false,
      lineStyle: {
        color: 'transparent',
      },
    },
    label: {
      show: false,
      color: 'transparent',
      position: 'outside',
      formatter: '{b} \n{c} {d}%',
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      viewControl: {
        autoRotate: true,
        alpha: 25, // 调整俯视角度，让底座更明显
        beta: 0,
        distance: 200, // 调整距离
        rotateSensitivity: 1,
        zoomSensitivity: 1,
        panSensitivity: 1,
      },
      top: 0,
      left: 'center',
      width: '100%',
      height: '100%',
      show: false,
      boxHeight: 6, // 增加高度让3D效果更明显
      light: {
        main: {
          intensity: 1.2,
          shadow: true,
          shadowQuality: 'high',
          alpha: 40,
          beta: 40,
        },
        ambient: {
          intensity: 0.3,
        },
      },
    },
    series: series,
  }
  return option
}

// 传入数据生成 option
const option = getPie3D(
  [
    {
      name: '2020-2022',
      value: 134,
      itemStyle: {
        color: '#55d0e0', // 青蓝色
      },
    },
    {
      name: '2022-2023',
      value: 56,
      itemStyle: {
        color: '#f7b731', // 黄色
      },
    },
    {
      name: '2024-2025',
      value: 57,
      itemStyle: {
        color: '#5a9fd4', // 蓝色
      },
    },
  ],
  0.5,
)

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>
<style scoped>
@import '@/styles/index.css';

.chart-section {
  width: 348px;
  height: 224px;
  display: flex;
  flex-direction: column;
}

.donut-chart {
  width: 100%;
  height: 100%;
}

.legend-section {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #ffffff;
  font-family: Noto Sans SC;
}

.data-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-left: 20px;
}

.data-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
}

.data-row-label {
  font-size: 14px;
  color: #ffffff;
  font-family: Noto Sans SC;
  width: 120px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.progress-fill {
  position: relative;
  height: 100%;
  background: linear-gradient(270deg, #ffc61a 0%, rgba(255, 198, 26, 0.45) 100%);
  transition: width 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    right: 0;
    width: 4px;
    height: 16px;
    background-color: #ffc61a;
    z-index: 1;
  }
}

.data-row-value {
  font-size: 16px;
  font-weight: bold;
  color: #f7b731;
  font-family: Noto Sans SC;
  min-width: 60px;
  text-align: right;
}
</style>
