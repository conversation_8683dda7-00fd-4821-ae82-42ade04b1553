<template>
  <div class="panel-container">
    <div class="panel-header">
      <div class="header-title">风险隐患监管</div>
    </div>
    <div class="flex items-center gap-4 p-4 panel-content">
      <div>
        <div class="mb-6 risk-indicators">
          <div class="risk-item red-level">
            <div class="risk-icon level-1 animate-pulse">11</div>
            <div class="risk-label">一级风险</div>
          </div>
          <div class="risk-item">
            <div class="risk-icon level-2 animate-pulse">13</div>
            <div class="risk-label">二级风险</div>
          </div>
          <div class="risk-item">
            <div class="risk-icon level-3 animate-pulse">18</div>
            <div class="risk-label">三级风险</div>
          </div>
          <div class="risk-item">
            <div class="risk-icon level-4 animate-pulse">25</div>
            <div class="risk-label">四级风险</div>
          </div>
        </div>
        <div class="status-indicators">
          <div class="status-item">
            <div class="status-value level-3 animate-pulse">42</div>
            <div class="status-label">现有隐患</div>
          </div>
          <div class="status-item">
            <div class="status-value level-4 animate-pulse">82647</div>
            <div class="status-label">已解决</div>
          </div>
          <div class="status-item">
            <div class="status-value level-5 animate-pulse">99.95%</div>
            <div class="status-label">处置率</div>
          </div>
        </div>
      </div>
      <div class="chart-section">
        <div ref="chartRef" class="chart-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '0%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(42, 56, 77, 0.6)',
    borderWidth: 0,
  },
  xAxis: {
    type: 'category',
    data: ['雄州镇', '安新镇', '容城镇', '大王镇', '小里镇', '大河镇', '八于乡', '其他乡镇'],
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: 'value',
    name: '单位: 项',
    nameTextStyle: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
        type: 'dashed',
      },
    },
  },
  legend: {
    data: ['现存风险', '现存隐患'],
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 20,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 24,
    top: 0,
  },
  series: [
    // 风险主柱体
    {
      name: '现存风险',
      type: 'bar',
      data: [5, 4, 5, 3, 3, 4, 2, 41],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 198, 26, 0.6)' },
          { offset: 1, color: 'rgba(255, 198, 26, 0.15)' },
        ]),
      },
      barWidth: 16,
      z: 2,
    },
    // 隐患主柱体
    {
      name: '现存隐患',
      type: 'bar',
      data: [3, 5, 2, 4, 3, 1, 3, 21],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(153, 178, 255, 0.6)' },
          { offset: 1, color: 'rgba(153, 178, 255, 0.15)' },
        ]),
      },
      barWidth: 16,
      z: 2,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [16, 2],
      symbolOffset: ['-55%', 0],
      z: 10,
      itemStyle: {
        color: '#FFC61A',
      },
      tooltip: { show: false },
      data: [5, 4, 5, 3, 3, 4, 2, 41],
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [16, 2],
      symbolOffset: ['55%', 0],
      z: 10,
      itemStyle: {
        color: '#99B2FF',
      },
      tooltip: { show: false },
      data: [3, 5, 2, 4, 3, 1, 3, 21],
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.risk-indicators {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.risk-item,
.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 70px;
}

.risk-icon,
.status-value {
  width: 63px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.risk-icon.level-1 {
  width: 63px;
  height: 42px;
  background: url('@/assets/risk/red-bg.png') no-repeat center;
  background-size: 100% 100%;
}

.risk-icon.level-2 {
  width: 63px;
  height: 42px;
  background: url('@/assets/risk/orange-bg.png') no-repeat center;
  background-size: 100% 100%;
}

.risk-icon.level-3,
.status-value.level-3 {
  width: 63px;
  height: 42px;
  background: url('@/assets/risk/yellow-bg.png') no-repeat center;
  background-size: 100% 100%;
}

.risk-icon.level-4,
.status-value.level-4 {
  width: 63px;
  height: 42px;
  background: url('@/assets/risk/green-bg.png') no-repeat center;
  background-size: 100% 100%;
}

.status-value.level-5 {
  width: 63px;
  height: 42px;
  background: url('@/assets/risk/blue-bg.png') no-repeat center;
  background-size: 100% 100%;
}

.risk-label,
.status-label {
  color: #fff;
  font-size: 10px;
  text-align: center;
}

.status-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.chart-section {
  width: 580px;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
