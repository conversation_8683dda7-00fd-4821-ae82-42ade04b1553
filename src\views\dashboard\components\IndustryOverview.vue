<template>
  <div class="panel-container">
    <div class="panel-header">行业概况</div>
    <div>
      <ul class="p-4">
        <li v-for="(row, idx) in indList" :key="idx" class="flex justify-between flex-none">
          <p v-for="item in row" :key="item.id" class="flex items-center ind-item">
            <span class="w-[6em] text-sm flex-none">{{ item.name }}</span>
            <span class="text-[#66FFFF] text-xl text-right grow">{{ item.value }}</span>
            <span class="text-[#66FFFF] text-sm">{{ item.unit }}</span>
          </p>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const indList = ref([
  [
    { name: '管道燃气企业', value: 3, unit: '家', id: 0 },
    { name: '瓶装燃气企业', value: 2, unit: '家', id: 1 },
    { name: '加气站企业', value: 4, unit: '家', id: 2 },
    { name: '管网长度', value: 116.2, unit: 'km', id: 3 },
  ],
  [
    { name: '门站数量', value: 9, unit: '个', id: 4 },
    { name: '储备站数量', value: 10, unit: '个', id: 5 },
    { name: '调压站数量', value: 16, unit: '个', id: 6 },
    { name: '阀门井数量', value: 139, unit: '个', id: 7 },
  ],
  [
    { name: '管道居民用户', value: 126941, unit: '户', id: 8 },
    { name: '瓶装居民用户', value: 15680, unit: '户', id: 9 },
    { name: '管道单位用户', value: 276, unit: '户', id: 10 },
    { name: '瓶装单位用户', value: 2203, unit: '户', id: 11 },
  ],
  [
    { name: '移动加气用户', value: 12364, unit: '户', id: 12 },
    { name: '售后服务站点', value: 16, unit: '个', id: 13 },
    { name: '从业人员数量', value: 259, unit: '人', id: 14 },
    { name: '用户监测设备', value: 136215, unit: '台', id: 15 },
  ],
])
</script>
<style scoped>
@import '@/styles/index.css';

.ind-item {
  width: 166px;
  height: 56px;
  line-height: 56px;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(153, 213, 255, 0.3);
}
</style>
