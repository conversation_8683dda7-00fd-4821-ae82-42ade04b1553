<template>
  <div class="contain">
    <screen-header></screen-header>
    <slot />
    <!-- <screen-footer @onWarning="handleLeakWarning"></screen-footer> -->
    <screen-map></screen-map>
    <screen-warning :open="isShowWarning"></screen-warning>
  </div>
</template>
<script setup lang="ts">
import screenHeader from './components/Header.vue'
// import screenFooter from './components/Footer.vue'
import screenWarning from './components/Warning.vue'
import screenMap from '../components/Map.vue'
import { ref } from 'vue'

const isShowWarning = ref(false)

// const handleLeakWarning = (value: { isShow: boolean; id: string }) => {
//   console.log('leak warning:', value)
//   // 展示全屏警报
//   isShowWarning.value = value.isShow
// }
</script>
<style scoped>
.contain {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
