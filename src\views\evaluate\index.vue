<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <ResourceOverview />
      <BottledStatusCover />
      <IndustryTrendLine />
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel">
      <!-- 地图控制组件 -->
      <LayersTool v-model="layersData" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ResourceOverview from './components/ResourceOverview.vue'
import BottledStatusCover from './components/BottledStatusCover.vue'
import IndustryTrendLine from './components/IndustryTrendLine.vue'
import LayersTool from '@/components/LayersTool.vue'
import { layerData } from './layerData'

// 图层数据
const layersData = ref(layerData)

onMounted(() => {
  // todo
})

onUnmounted(() => {
  // 清除所有图层
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.left-panel {
  left: 0;
}

.right-panel {
  right: 0;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
